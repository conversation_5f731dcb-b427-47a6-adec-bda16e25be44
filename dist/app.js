"use strict";
// dappLookerApi.ts
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTokenMarketData = getTokenMarketData;
const node_fetch_1 = __importDefault(require("node-fetch")); // Remove this line if running in the browser
async function getTokenMarketData(params) {
    const { apiKey, chain, token_tickers, token_addresses, token_ids, ecosystem, page, } = params;
    console.log("We are here...149");
    const url = new URL('https://api.dapplooker.com/v1/crypto-market');
    url.searchParams.append('api_key', apiKey);
    url.searchParams.append('chain', chain);
    if (token_tickers)
        url.searchParams.append('token_tickers', token_tickers);
    if (token_addresses)
        url.searchParams.append('token_addresses', token_addresses);
    if (token_ids)
        url.searchParams.append('token_ids', token_ids);
    if (ecosystem)
        url.searchParams.append('ecosystem', ecosystem);
    if (page)
        url.searchParams.append('page', page.toString());
    console.log(`Calling with ${JSON.stringify(params)}`);
    const response = await (0, node_fetch_1.default)(url.toString());
    if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }
    const data = await response.json();
    return data;
}
// --- Example Usage ---
(async () => {
    try {
        const result = await getTokenMarketData({
            apiKey: '1ae22d456002428bab90c14e7d30ff85',
            chain: 'base',
            token_tickers: 'AIXBT',
        });
        console.log(result.data);
    }
    catch (err) {
        console.error(err);
    }
})();
