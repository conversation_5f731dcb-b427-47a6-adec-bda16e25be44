{"name": "qs", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "homepage": "https://github.com/ljharb/qs", "version": "6.7.0", "repository": {"type": "git", "url": "https://github.com/ljharb/qs.git"}, "main": "lib/index.js", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "engines": {"node": ">=0.6"}, "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^13.1.1", "browserify": "^16.2.3", "covert": "^1.1.1", "editorconfig-tools": "^0.1.1", "eslint": "^5.15.3", "evalmd": "^0.0.17", "for-each": "^0.3.3", "iconv-lite": "^0.4.24", "mkdirp": "^0.5.1", "object-inspect": "^1.6.0", "qs-iconv": "^1.0.4", "safe-publish-latest": "^1.1.2", "safer-buffer": "^2.1.2", "tape": "^4.10.1"}, "scripts": {"prepublish": "safe-publish-latest && npm run dist", "pretest": "npm run --silent readme && npm run --silent lint", "test": "npm run --silent coverage", "tests-only": "node test", "readme": "evalmd README.md", "postlint": "editorconfig-tools check * lib/* test/*", "lint": "eslint lib/*.js test/*.js", "coverage": "covert test", "dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js"}, "license": "BSD-3-<PERSON><PERSON>"}