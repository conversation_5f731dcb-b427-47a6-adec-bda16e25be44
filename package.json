{"name": "loky-api-test", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "ts-node src/app.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/express": "^4.17.1", "@types/node": "^24.0.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@types/node-fetch": "^2.6.12", "express": "^4.17.1", "node-fetch": "^3.3.2"}}