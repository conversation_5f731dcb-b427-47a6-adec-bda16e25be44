// dappLookerApi.ts

// Using built-in fetch API (Node.js 18+)

// --- Type Definitions ---

export interface TokenInfo {
    id: string;
    symbol: string;
    name: string;
    handle: string;
    description: string;
    ca: string;
    chain: string;
    ecosystem: string;
    image?: string;
}

export interface TechnicalIndicators {
    support: string;
    resistance: string;
    rsi: string;
    sma: string;
}

export interface TokenHolderInsights {
    total_holder_count: number;
    holder_count_change_percentage_24h: string;
    fifty_percentage_holding_wallet_count: string;
    first_100_buyers_initial_bought: string;
    first_100_buyers_initial_bought_percentage: string;
    first_100_buyers_current_holding: string;
    first_100_buyers_current_holding_percentage: string;
    top_10_holder_balance: string;
    top_10_holder_percentage: string;
    top_50_holder_balance: string;
    top_50_holder_percentage: string;
    top_100_holder_balance: string;
    top_100_holder_percentage: string;
}

export interface SmartMoneyInsights {
    top_25_holder_buy_24h: string;
    top_25_holder_sold_24h: string;
}

export interface DevWalletInsights {
    wallet_address: string;
    wallet_balance: string | null;
    dev_wallet_total_holding_percentage: string | null;
    dev_wallet_outflow_txs_count_24h: string | null;
    dev_wallet_outflow_amount_24h: string | null;
    fresh_wallet: boolean;
    dev_sold: boolean;
    dev_sold_percentage: string;
    bundle_wallet_count: number;
    bundle_wallet_supply_percentage: string | null;
}

export interface TokenMetrics {
    usd_price: string;
    mcap: string;
    fdv: string;
    volume_24h: string;
    total_liquidity: string;
    price_change_percentage_1h: string;
    price_change_percentage_24h: string;
    price_change_percentage_7d: string;
    price_change_percentage_30d: string;
    volume_change_percentage_7d: string;
    volume_change_percentage_30d: string;
    mcap_change_percentage_7d: string;
    mcap_change_percentage_30d: string;
    price_high_24h: string;
    price_ath: string;
    circulating_supply: string;
    total_supply: string;
}

export interface XSocialMetrics {
    mindshare_3d: number;
    mindshare_change_percentage_3d: number;
    impression_count_3d: number;
    impression_count_change_percentage_3d: number;
    engagement_count_3d: number;
    engagement_count_change_percentage_3d: number;
    follower_count_3d: number;
    smart_follower_count_3d: number;
    mindshare_7d: number;
    mindshare_change_percentage_7d: number;
    impression_count_7d: number;
    impression_count_change_percentage_7d: number;
    engagement_count_7d: number;
    engagement_count_change_percentage_7d: number;
    follower_count_7d: number;
    smart_follower_count_7d: number;
}

export interface TokenData {
    id: string;
    token_info: TokenInfo;
    technical_indicators: TechnicalIndicators;
    token_holder_insights: TokenHolderInsights;
    smart_money_insights: SmartMoneyInsights;
    dev_wallet_insights: DevWalletInsights;
    token_metrics: TokenMetrics;
    x_social_metrics: XSocialMetrics;
    last_updated_at: string;
}

export interface Pagination {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
}

export interface DappLookerApiResponse {
    success: boolean;
    data: TokenData[];
    meta: {
        pagination: Pagination;
    };
}

// --- API Function ---

export interface GetTokenMarketDataParams {
    apiKey: string;
    chain: string; // e.g. 'base', 'solana'
    token_tickers?: string; // comma-separated
    token_addresses?: string; // comma-separated
    token_ids?: string; // comma-separated
    ecosystem?: string; // e.g. 'virtuals'
    page?: number;
}

export async function getTokenMarketData(params: GetTokenMarketDataParams): Promise<DappLookerApiResponse> {
    const {
        apiKey,
        chain,
        token_tickers,
        token_addresses,
        token_ids,
        ecosystem,
        page,
    } = params;

    const url = new URL('https://api.dapplooker.com/v1/crypto-market');
    url.searchParams.append('api_key', apiKey);
    url.searchParams.append('chain', chain);
    if (token_tickers) url.searchParams.append('token_tickers', token_tickers);
    if (token_addresses) url.searchParams.append('token_addresses', token_addresses);
    if (token_ids) url.searchParams.append('token_ids', token_ids);
    if (ecosystem) url.searchParams.append('ecosystem', ecosystem);
    if (page) url.searchParams.append('page', page.toString());

    const response = await fetch(url.toString());
    if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }
    const data: DappLookerApiResponse = await response.json() as DappLookerApiResponse;
    return data;
}

(async () => {
    try {
        const result = await getTokenMarketData({
            apiKey: '1ae22d456002428bab90c14e7d30ff85',
            chain: 'base',
            token_addresses: '******************************************', // Loky
        });
        console.log(JSON.stringify(result.data, null, 4));
    } catch (err) {
        console.error(err);
    }
})();
